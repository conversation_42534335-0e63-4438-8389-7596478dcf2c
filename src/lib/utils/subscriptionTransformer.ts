import type { 
    SubscriptionQuotaData, 
    SubscriptionInfoData
} from '$lib/api/types/subscription-api';
import type { 
    SubscriptionData, 
    CurrentSubscription
} from '$lib/types/subscription';

/**
 * Transform backend API response to frontend component format
 */
export class SubscriptionTransformer {
    
    /**
     * Transform quota status data to current subscription format
     */
    static transformToCurrentSubscription(quotaData: SubscriptionQuotaData, infoData?: SubscriptionInfoData): CurrentSubscription {
        return {
            tier: quotaData.tier_name,
            status: quotaData.status,
            expiresAt: quotaData.expires_at,
            serialNumber: infoData?.subscription_key || 'N/A',
            organizationName: quotaData.organization_name,
            activatedOn: infoData?.activated_on
        };
    }

    /**
     * Transform complete backend data to frontend subscription data format
     */
    static transformToSubscriptionData(
        quotaData: SubscriptionQuotaData,
        infoData: SubscriptionInfoData | null,
    ): SubscriptionData {
        const currentSubscription = this.transformToCurrentSubscription(quotaData, infoData || undefined);

        return {
            currentSubscription,
            quotaData,
        };
    }
}
